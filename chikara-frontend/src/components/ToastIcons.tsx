import React from "react";

interface IconProps {
    size?: number;
    className?: string;
}

// Success Icon - Simple checkmark
export const SuccessIcon: React.FC<IconProps> = ({ size = 20, className = "" }) => (
    <svg
        width={size}
        height={size}
        viewBox="0 0 24 24"
        fill="none"
        className={className}
        xmlns="http://www.w3.org/2000/svg"
    >
        {/* Checkmark */}
        <path
            d="M8 12.5L10.5 15L16 9.5"
            stroke="currentColor"
            strokeWidth="2.5"
            strokeLinecap="round"
            strokeLinejoin="round"
            fill="none"
        />
    </svg>
);

// Error Icon - Simple X mark
export const ErrorIcon: React.FC<IconProps> = ({ size = 20, className = "" }) => (
    <svg
        width={size}
        height={size}
        viewBox="0 0 24 24"
        fill="none"
        className={className}
        xmlns="http://www.w3.org/2000/svg"
    >
        {/* X mark */}
        <path
            d="M8 8L16 16M8 16L16 8"
            stroke="currentColor"
            strokeWidth="2.5"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </svg>
);

// Loading Icon - Simple spinning circle
export const LoadingIcon: React.FC<IconProps> = ({ size = 20, className = "" }) => (
    <svg
        width={size}
        height={size}
        viewBox="0 0 24 24"
        fill="none"
        className={className}
        xmlns="http://www.w3.org/2000/svg"
    >
        {/* Spinning circle */}
        <circle
            cx="12"
            cy="12"
            r="8"
            stroke="currentColor"
            strokeWidth="2.5"
            strokeLinecap="round"
            strokeDasharray="32"
            strokeDashoffset="32"
            fill="none"
        >
            <animateTransform
                attributeName="transform"
                type="rotate"
                values="0 12 12;360 12 12"
                dur="1s"
                repeatCount="indefinite"
            />
            <animate attributeName="stroke-dashoffset" values="32;0;32" dur="1.5s" repeatCount="indefinite" />
        </circle>
    </svg>
);

// Info Icon - Simple information symbol
export const InfoIcon: React.FC<IconProps> = ({ size = 20, className = "" }) => (
    <svg
        width={size}
        height={size}
        viewBox="0 0 24 24"
        fill="none"
        className={className}
        xmlns="http://www.w3.org/2000/svg"
    >
        {/* Information symbol */}
        <circle cx="12" cy="8" r="1.5" fill="currentColor" />
        <rect x="11" y="11" width="2" height="7" rx="1" fill="currentColor" />
    </svg>
);
