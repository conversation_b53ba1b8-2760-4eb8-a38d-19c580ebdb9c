import React, { useState, useEffect } from "react";
import useCheckMobileScreen from "@/hooks/useCheckMobileScreen";
import { ToastBar, Toaster, toast } from "react-hot-toast";
import { X } from "lucide-react";
import { useLocation } from "react-router-dom";
import { SuccessIcon, ErrorIcon, LoadingIcon, InfoIcon } from "./ToastIcons";

// Helper function to get toast styling based on type
const getToastStyle = (type: string) => {
    switch (type) {
        case "success":
            return {
                icon: SuccessIcon,
                accentColor: "bg-green-500",
                iconBg: "bg-green-500",
            };
        case "error":
            return {
                icon: ErrorIcon,
                accentColor: "bg-red-500",
                iconBg: "bg-red-500",
            };
        case "loading":
            return {
                icon: LoadingIcon,
                accentColor: "bg-blue-500",
                iconBg: "bg-blue-500",
            };
        default:
            return {
                icon: InfoIcon,
                accentColor: "bg-violet-500",
                iconBg: "bg-violet-500",
            };
    }
};

const ToastManager = () => {
    const isMobile = useCheckMobileScreen();
    const location = useLocation();
    const isChatPage = location.pathname === "/chat";
    const [windowWidth, setWindowWidth] = useState(window.innerWidth);

    // Handle window resize to update toast positioning
    useEffect(() => {
        const handleResize = () => setWindowWidth(window.innerWidth);
        window.addEventListener("resize", handleResize);
        return () => window.removeEventListener("resize", handleResize);
    }, []);

    let containerStyle: React.CSSProperties = {};
    if (isMobile) {
        containerStyle = {
            top: 65,
        };
    } else {
        // Position toasts in the top right of the main content container
        // Account for:
        // - Navbar height (60px on regular, 80px on 2xl)
        // - Minimal space for potential PageBanner
        // - Chatbox width (21.5vw on xl, 25.51vw on 2xl screens) when visible
        // - Small padding from the edge
        const navbarHeight = windowWidth >= 1536 ? 80 : 60; // 2xl breakpoint
        containerStyle = {
            top: navbarHeight + 16, // Navbar + minimal padding to be in top corner of content
        };

        // If chatbox is visible (not on chat page), account for its width
        if (!isChatPage) {
            containerStyle.right = "calc(21.5vw + 1rem)"; // Chatbox width + padding for xl screens

            // For 2xl screens, adjust for larger chatbox width
            if (windowWidth >= 1536) {
                // 2xl breakpoint
                containerStyle.right = "calc(25.51vw + 1rem)";
            }
        } else {
            // On chat page, chatbox is hidden, so position closer to right edge
            containerStyle.right = "1rem";
        }
    }

    return (
        <Toaster
            position={isMobile ? "top-center" : "top-right"}
            containerStyle={containerStyle}
            toastOptions={{
                duration: 4000,
                style: {
                    background: "transparent",
                    border: "none",
                    boxShadow: "none",
                    padding: 0,
                    maxWidth: isMobile ? "20rem" : "24rem",
                },
            }}
        >
            {(t) => (
                <ToastBar toast={t}>
                    {({ message }) => {
                        const toastStyle = getToastStyle(t.type);
                        const IconComponent = toastStyle.icon;

                        // Parse message to extract title and subtitle
                        const messageStr = typeof message === "string" ? message : String(message);
                        const lines = messageStr.split("\n");
                        const title = lines[0] || messageStr;
                        const subtitle = lines[1] || "";

                        return (
                            <div
                                className={`
                                    relative overflow-hidden rounded-xl
                                    bg-gray-800 border border-gray-700
                                    shadow-2xl cursor-pointer group
                                    hover:scale-[1.02] transition-all duration-200 ease-out
                                    ${isMobile ? "max-w-sm mx-2" : "max-w-md"}
                                    backdrop-blur-sm
                                `}
                                onClick={() => toast.dismiss(t.id)}
                            >
                                {/* Content */}
                                <div className="relative flex items-start gap-4 p-4">
                                    {/* Icon */}
                                    <div
                                        className={`flex-shrink-0 w-10 h-10 rounded-full ${toastStyle.iconBg} flex items-center justify-center`}
                                    >
                                        <IconComponent size={20} className="text-white" />
                                    </div>

                                    {/* Message Content */}
                                    <div className="flex-1 min-w-0 pt-1">
                                        <div className="text-white font-semibold text-base leading-tight mb-1">
                                            {title}
                                        </div>
                                        {subtitle && (
                                            <div className="text-gray-400 text-sm leading-tight">{subtitle}</div>
                                        )}
                                    </div>

                                    {/* Close button */}
                                    <button
                                        className="flex-shrink-0 text-gray-400 hover:text-white transition-colors duration-200 p-1 rounded-md hover:bg-gray-700"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            toast.dismiss(t.id);
                                        }}
                                    >
                                        <X size={16} strokeWidth={1.5} />
                                    </button>
                                </div>

                                {/* Bottom accent line */}
                                <div
                                    className={`absolute bottom-0 left-0 right-0 h-1 ${toastStyle.accentColor} rounded-b-xl`}
                                />
                            </div>
                        );
                    }}
                </ToastBar>
            )}
        </Toaster>
    );
};

export default ToastManager;
